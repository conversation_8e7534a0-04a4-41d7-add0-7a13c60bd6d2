import React from 'react';
import { View, Image, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface AvatarProps {
  uri?: string | null;
  username?: string | null;
  size?: number;
}

export function Avatar({ uri, username, size = 40 }: AvatarProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme, size);

  // Get initials from username
  const getInitials = (name?: string | null): string => {
    if (!name) return '?';

    const parts = name.trim().split(/\s+/);
    if (parts.length === 0) return '?';

    if (parts.length === 1) {
      return parts[0].charAt(0).toUpperCase();
    }

    return (
      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
    ).toUpperCase();
  };

  // If there's a valid URI, render the image
  if (uri) {
    return (
      <View style={styles.container}>
        <Image source={{ uri }} style={styles.image} />
      </View>
    );
  }

  // Otherwise, render a fallback with initials
  return (
    <View style={styles.container}>
      <Text style={styles.fallbackText}>{getInitials(username)}</Text>
    </View>
  );
}
