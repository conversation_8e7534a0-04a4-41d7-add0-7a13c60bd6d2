import React, { createContext, useMemo } from 'react';
// Update store and theme import paths
import { useSettingsStore } from '@/lib/store/settingsStore'; 
import { lightTheme, darkTheme, AppTheme } from './themes';

interface ThemeContextProps {
  theme: AppTheme;
}

export const ThemeContext = createContext<ThemeContextProps>({
  theme: lightTheme, // Default theme
});

export const ThemeProvider: React.FC<React.PropsWithChildren<{}>> = ({ children }) => {
  const themeMode = useSettingsStore((state) => state.themeMode);
  const systemColorScheme = useSettingsStore((state) => state.systemColorScheme);
  const hasHydrated = useSettingsStore((state) => state._hasHydrated);

  const activeTheme = useMemo(() => {
    if (!hasHydrated) {
      // Prevent hydration mismatch on server/client by returning default initially
      return lightTheme;
    }
    const mode = themeMode === 'system' ? systemColorScheme : themeMode;
    return mode === 'dark' ? darkTheme : lightTheme;
  }, [themeMode, systemColorScheme, hasHydrated]);

  return (
    <ThemeContext.Provider value={{ theme: activeTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}; 