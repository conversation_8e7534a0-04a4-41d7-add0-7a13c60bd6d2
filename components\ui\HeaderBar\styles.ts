import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.background, // Set background
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightContainer: {
    // Styles for the right element container if needed
  },
  backButton: {
    marginRight: theme.spacing.sm,
    padding: theme.spacing.xs, // Add padding for touch area
  },
  title: {
    fontFamily: theme.fonts.bold,
    fontSize: 20, // Adjust size as needed
    color: theme.colors.text,
  },
  subtitle: {
    fontFamily: theme.fonts.regular,
    fontSize: 12, // Adjust size as needed
    color: theme.colors.secondaryText,
    marginTop: 2,
  },
});
