import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme, size: number = 40) => StyleSheet.create({
  container: {
    width: size,
    height: size,
    borderRadius: size / 2,
    overflow: 'hidden',
    backgroundColor: theme.colors.cardBackground,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  image: {
    width: size,
    height: size,
  },
  fallbackText: {
    color: theme.colors.primary,
    fontSize: size / 2.5,
    fontWeight: 'bold',
  },
});
