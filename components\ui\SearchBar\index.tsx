import React from 'react';
import { View, TextInput, TouchableOpacity } from 'react-native';
import { Search, X } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';

interface SearchBarProps {
  value?: string;
  onChangeText?: (text: string) => void;
  onSearch?: (text: string) => void;
  autoFocus?: boolean;
  placeholder?: string;
}

export default function SearchBar({
  value,
  onChangeText,
  onSearch,
  autoFocus = false,
  placeholder,
}: SearchBarProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const [searchText, setSearchText] = React.useState(value || '');

  // 如果外部 value 变化，更新内部状态
  React.useEffect(() => {
    if (value !== undefined) {
      setSearchText(value);
    }
  }, [value]);

  const handleChangeText = (text: string) => {
    // 如果提供了外部 onChangeText，则调用它
    if (onChangeText) {
      onChangeText(text);
    } else {
      // 否则使用内部状态
      setSearchText(text);
    }
  };

  const handleSubmit = () => {
    const textToSearch = value !== undefined ? value : searchText;
    if (onSearch && textToSearch.trim()) {
      onSearch(textToSearch);
    }
  };

  const handleClear = () => {
    if (onChangeText) {
      onChangeText('');
    }
    setSearchText('');
  };

  return (
    <View style={styles.container}>
      <Search
        size={18}
        color={theme.colors.secondaryText}
        style={styles.searchIcon}
      />

      <TextInput
        style={styles.input}
        placeholder={
          placeholder || t('searchPlaceholder', '搜索故事、作者、标签...')
        }
        placeholderTextColor={theme.colors.placeholder}
        value={value !== undefined ? value : searchText}
        onChangeText={handleChangeText}
        onSubmitEditing={handleSubmit}
        returnKeyType="search"
        autoFocus={autoFocus}
      />

      {(value || searchText) && (value || searchText).length > 0 && (
        <TouchableOpacity style={styles.clearButton} onPress={handleClear}>
          <X size={16} color={theme.colors.secondaryText} />
        </TouchableOpacity>
      )}
    </View>
  );
}
