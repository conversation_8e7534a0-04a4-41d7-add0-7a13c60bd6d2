import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { ChevronDown, Check } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';

export interface SortOption {
  id: string;
  label: string;
}

interface SortSelectorProps {
  options: SortOption[];
  selectedId: string;
  onSelect: (id: string) => void;
  label?: string;
}

export default function SortSelector({
  options,
  selectedId,
  onSelect,
  label,
}: SortSelectorProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const selectedOption = options.find((option) => option.id === selectedId);

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setModalVisible(true)}
      >
        <Text style={styles.selectorText}>
          {selectedOption?.label || options[0]?.label}
        </Text>
        <ChevronDown size={16} color={theme.colors.text} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
        >
          <View style={styles.modalContent}>
            {options.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={styles.optionItem}
                onPress={() => {
                  onSelect(option.id);
                  setModalVisible(false);
                }}
              >
                <Text style={styles.optionText}>{option.label}</Text>
                {option.id === selectedId && (
                  <Check size={16} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}