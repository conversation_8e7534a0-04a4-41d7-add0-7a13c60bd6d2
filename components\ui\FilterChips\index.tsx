import React from 'react';
import { View, ScrollView, TouchableOpacity, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

export interface FilterOption {
  id: string;
  label: string;
}

interface FilterChipsProps {
  options: FilterOption[];
  selectedIds: string[];
  onSelect: (id: string) => void;
  multiSelect?: boolean;
}

export default function FilterChips({
  options,
  selectedIds,
  onSelect,
  multiSelect = false,
}: FilterChipsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  const handleSelect = (id: string) => {
    onSelect(id);
  };

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
    >
      {options.map((option) => {
        const isSelected = selectedIds.includes(option.id);
        return (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.chip,
              isSelected && { backgroundColor: theme.colors.primary },
            ]}
            onPress={() => handleSelect(option.id)}
          >
            <Text
              style={[
                styles.chipText,
                isSelected && { color: theme.colors.background },
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
}