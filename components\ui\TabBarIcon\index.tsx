import React, { ReactNode } from 'react';
import { View } from 'react-native';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface TabBarIconProps {
  children: ReactNode;
}

export default function TabBarIcon({ children }: TabBarIconProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.container}>
      {children}
    </View>
  );
}
